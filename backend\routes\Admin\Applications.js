const express = require("express");
const { VerifyTokenAuth } = require("../../middlewares/AdminAuth");
const { appliedCampaigns, Campaign } = require("../../database");
const { Parser } = require("json2csv");
const mongoose = require("mongoose");
const application = express.Router();

application.get("/:id", VerifyTokenAuth, async (req, res) => {
  try {
    const limit = 100;
    const cursor = req.query.cursor;
    const {id} = req.params

    let query = {
      campaign: id
    };
    if (cursor) {
      query._id = { $gt: cursor };
    }

    const campaign = await Campaign.find({_id : id});

    const results = await appliedCampaigns
      .find(query)
      .sort({ _id: 1 })
      .limit(limit + 1)

    const hasMore = results.length > limit;
    const limitedResults = hasMore ? results.slice(0, limit) : results;

    const applications = limitedResults.map((item) => ({
      id: item._id,
      name: item.name,
      email: item.email,
      phone : item.phone,
      address: `${item.address}, ${item.city}, ${item.state}, ${item.zipCode}`,
      appliedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(),
      status: item.status,
      rejectionReason: item.rejectionReason,
      showReasonToInfluencer: item.showReasonToInfluencer,
    }));
    
    res.json({
      status: "success",
      applications,
      nextCursor: hasMore
        ? limitedResults[limitedResults.length - 1]._id
        : null,
      isLastPage: !hasMore,
      campaignTitle : campaign[0].campaignTitle
    });
  } catch (error) {
    res.status(500).json({ message: "Something went wrong", error });
  }
});

application.post("/paginate/:id", VerifyTokenAuth, async (req, res) => {
  try {
    const limit = 50;
    const { LastId } = req.body;
    const {id} = req.params
    const ObjectId = mongoose.Types.ObjectId;
    let query = {
      campaign : id
    };
    if (LastId) {
      query._id = { $gt: new ObjectId(LastId.toString()) };
    }

    const results = await appliedCampaigns
      .find(query)
      .sort({ _id: 1 })
      .limit(limit + 1)

    const hasMore = results.length > limit;
    const limitedResults = hasMore ? results.slice(0, limit) : results;

    const applications = limitedResults.map((item) => ({
      id: item._id,
      name: item.name,
      email: item.email,
      phone: item.phone,
      address: `${item.address}, ${item.city}, ${item.state}, ${item.zipCode}`,
      appliedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(),
    }));

    console.log(applications)

    res.json({
      status: "success",
      applications,
      nextCursor: hasMore
        ? limitedResults[limitedResults.length - 1]._id
        : null,
      isLastPage: !hasMore,
    });
  } catch (error) {
    res.status(500).json({ message: "Something went wrong", error });
  }
});

application.get("/download/:id", async (req, res) => {
  try {
    const {id} = req.params
    const result = await appliedCampaigns.find({campaign: id}).sort({
      createdAt: -1,
    });
    const applications = await result.map((item) => {
      return {
        campaign: `https://machably.com/campaign/${item.campaign}`,
        name: item.name,
        email: item.email,
        phone: item.phone,
        address: `${item.address}, ${item.city}, ${item.state}, ${item.zipCode}`,
        appliedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(),
      };
    });


    const fields = ["campaign", "name", "email", "address", "appliedAt"];
    const opts = {
      fields,
      quote: '"', // wrap all values in double quotes
      delimiter: ",", // standard comma-delimiter
    };

    const json2csvParser = new Parser(opts);
    const csv = json2csvParser.parse(applications);

    res.header("Content-Type", "text/csv");
    res.attachment("applications.csv");
    res.send(csv);
  } catch {
    res.json({
      status: "failed",
      message: "something went wrong",
    });
  }
});
module.exports = {
  application,
};


application.patch("/:applicationId/status", VerifyTokenAuth, async (req, res) => {
  const { applicationId } = req.params;
  const { status, rejectionReason, showReasonToInfluencer } = req.body;

  if (!["Approved", "Rejected", "Pending"].includes(status)) {
    return res.json({ status: "failed", message: "Invalid status" });
  }

  try {
    const application = await appliedCampaigns.findById(applicationId);
    if (!application) {
      return res.json({ status: "failed", message: "Application not found" });
    }

    application.status = status;

    if (status === "Rejected") {
      application.rejectionReason = rejectionReason || "";
      application.showReasonToInfluencer = !!showReasonToInfluencer;
    } else {
      application.rejectionReason = "";
      application.showReasonToInfluencer = false;
    }

    await application.save();

    res.json({
      status: "success",
      message: `Application marked as ${status}`,
      application, // return updated doc
    });    
  } catch (err) {
    console.error("Error updating application status:", err);
    res.json({ status: "failed", message: "Server error" });
  }
});



application.delete("/:campaignId/:applicantId", VerifyTokenAuth, async (req, res) => {
  try {
    const { applicantId } = req.params;
    const deletedApplicant = await appliedCampaigns.findByIdAndDelete(applicantId);

    if (!deletedApplicant) {
      return res.status(404).json({
        status: "error",
        message: "Applicant not found",
      });
    }

    res.status(200).json({
      status: "success",
      message: "Applicant deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting applicant:", error);
    res.status(500).json({
      status: "error",
      message: "Server error while deleting applicant",
    });
  }
});

