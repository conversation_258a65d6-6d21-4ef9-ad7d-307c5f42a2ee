const nodemailer = require("nodemailer");
const dotenv = require("dotenv");
const { User } = require("../database");
const { FeedbackAsk_Email } = require("../emails/feedbackAsk");
const { CampaignSend } = require("../emails/broadCast");
const { default: axios } = require("axios");
dotenv.config();


const SMTP_USER = process.env.SMTP_USER;
const SMTP_PASS = process.env.SMTP_PASS;
const SMTP_HOST = process.env.SMTP_HOST;
const SMTP_PORT = process.env.SMTP_PORT;
const SMTP_SEND_EMAIL = process.env.SMTP_SEND_EMAIL
const SMTP_API_KEY=process.env.SMTP_API_KEY


const transporter = nodemailer.createTransport({
  host: SMTP_HOST,
  port: SMTP_PORT,
  // secure: false,
  auth: {
    user: SMTP_USER,
    pass: SMTP_PASS,
  },
});

// Use the same transporter for all emails to avoid conflicts
const otpTransporter = transporter;

const sendWelcomeEmail = async (userEmail, name) => {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: userEmail,
      subject: "Thank you for joining!",
      html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>Thanks for Joining</title>
      </head>
      <body style="margin: 0; padding: 0; background-color: #000000; color: #ffffff; font-family: Arial, sans-serif;">
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" height="100%">
          <tr>
            <td align="center" valign="middle">
              <h1 style="color: #ffffff; font-size: 28px; text-align: center;">Hi ${name}, thanks for joining!</h1>
            </td>
          </tr>
        </table>
      </body>
      </html>
      `,
    };

    try {
      await addToBrevoContacts(userEmail, name);
    } catch (err) {
      console.error("⚠️ Brevo contact error (non-blocking):", err.message || err);
    }

    await transporter.sendMail(mailOptions); // ✅ only one arg
    console.log("✅ Welcome email sent");
  } catch (error) {
    console.error("❌ Error sending welcome email:", error.message || error);
  }
};


const sendCampaignToAllUsers = async (campaign) => {
  try {
    const cursor = User.find({}, 'email').cursor();

    for await (const user of cursor) {
      const mailOptions = {
        from: SMTP_SEND_EMAIL,
        to: user.email,
        subject: `New Opportunity Just for You!`,
        html: CampaignSend(campaign),
      };

      try {
        await transporter.sendMail(mailOptions);
        console.log(`✅ Email sent to ${user.email}`);
      } catch (err) {
        console.error(`❌ Failed to send to ${user.email}:`, err.message || err);
      }
    }
  } catch (err) {
    console.error("❌ Error in sendCampaignToAllUsers:", err.message || err);
  }
};

const sendThanksApplyig = async (userEmail, campaign, feedbackLink) => {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: userEmail,
      subject: "Thanks for applying",
      html: FeedbackAsk_Email(campaign, feedbackLink),
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Thanks email sent to ${userEmail}`);
  } catch (error) {
    console.error(`❌ Error sending thanks email to ${userEmail}:`, error.message || error);
  }
};


const addToBrevoContacts = async (email, name) => {
  try {
    const response = await axios.post(
      'https://api.brevo.com/v3/contacts',
      {
        email,
        attributes: {
          NAME: name || "", // Ensure name is always defined
        },
        updateEnabled: true,
      },
      {
        headers: {
          'api-key': SMTP_API_KEY,
          'Content-Type': 'application/json',
        },
      }
    );

    console.log(`✅ Contact added to Brevo: ${email} (ID: ${response.data.id})`);
  } catch (error) {
    const message = error.response?.data?.message || error.message;
    console.error(`❌ Error adding ${email} to Brevo:`, message);
  }
};



// ✅ NEW: Send Verification Email with Link
const sendVerificationLink = async (userEmail, token) => {
  try {
    const verificationUrl = `https://matchably.kr/verify-email?token=${token}`;
    
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: userEmail,
      subject: "Verify your email address - Matchably",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>Verify Your Email</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
          <table align="center" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
            <tr>
              <td style="text-align: center;">
                <h1 style="color: #4f46e5;">Matchably</h1>
                <h2 style="color: #333;">Email Verification</h2>
                <p style="color: #555; font-size: 16px;">Hi there,</p>
                <p style="color: #555; font-size: 16px;">Please click the button below to verify your email address and complete your registration:</p>
                <a href="${verificationUrl}" target="_blank" style="display: inline-block; margin-top: 20px; padding: 12px 25px; background-color: #4f46e5; color: white; text-decoration: none; border-radius: 5px; font-size: 16px;">
                  Verify My Email
                </a>
                <p style="margin-top: 30px; color: #777;">If the button doesn't work, copy and paste this link into your browser:</p>
                <p style="word-break: break-all; color: #4f46e5;">
                  <a href="${verificationUrl}" style="color: #4f46e5;">${verificationUrl}</a>
                </p>
                <p style="color: #999; margin-top: 30px;">This link will expire soon. If you didn’t request this, you can ignore this email.</p>
              </td>
            </tr>
          </table>
          <p style="text-align: center; color: #aaa; font-size: 12px; margin-top: 20px;">&copy; ${new Date().getFullYear()} Matchably. All rights reserved.</p>
        </body>
        </html>
      `,
    };

    await otpTransporter.sendMail(mailOptions);
    console.log(`✅ Verification email sent to ${userEmail}`);
  } catch (error) {
    const msg = error.response?.data || error.message || error;
    console.error(`❌ Failed to send verification email to ${userEmail}:`, msg);
  }
};







// ✅ Send temporary password email
const sendTempPasswordEmail = async (userEmail, tempPassword) => {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: userEmail,
      subject: "Temporary Password - Matchably",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Temporary Password</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
          <table role="presentation" style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 40px 0; text-align: center;">
                <table role="presentation" style="width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                  <tr>
                    <td style="padding: 40px 40px 20px 40px; text-align: center;">
                      <h1 style="color: #84cc16; margin: 0; font-size: 28px;">Matchably</h1>
                    </td>
                  </tr>
                  <tr>
                    <td style="padding: 0 40px 40px 40px;">
                      <h2 style="color: #333; margin: 0 0 20px 0; font-size: 24px;">Temporary Password</h2>
                      <p style="color: #666; margin: 0 0 20px 0; line-height: 1.6;">
                        We've generated a temporary password for your account. Please use this password to login and then change it immediately for security.
                      </p>
                      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0; text-align: center;">
                        <p style="margin: 0; color: #333; font-size: 14px;">Your temporary password:</p>
                        <p style="margin: 10px 0 0 0; font-size: 24px; font-weight: bold; color: #84cc16; letter-spacing: 2px;">${tempPassword}</p>
                      </div>
                      <p style="color: #666; margin: 20px 0 0 0; line-height: 1.6; font-size: 14px;">
                        <strong>Important:</strong> Please change this password immediately after logging in for security purposes.
                      </p>
                    </td>
                  </tr>
                </table>
                <p style="text-align: center; color: #aaa; font-size: 12px; margin-top: 20px;">&copy; ${new Date().getFullYear()} Matchably. All rights reserved.</p>
              </td>
            </tr>
          </table>
        </body>
        </html>
      `,
    };

    await otpTransporter.sendMail(mailOptions);
    console.log(`✅ Temporary password email sent to ${userEmail}`);
  } catch (error) {
    const msg = error.response?.data || error.message || error;
    console.error(`❌ Failed to send temporary password email to ${userEmail}:`, msg);
    throw error;
  }
};

// ✅ Send tracking notification email
const sendTrackingEmail = async (userEmail, userName, campaignTitle, trackingNumber) => {
  try {
    const trackingUrl = `https://www.17track.net/en/track?nums=${trackingNumber}`;

    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: userEmail,
      subject: "Your product has shipped!",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>Product Shipped</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
          <table align="center" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
            <tr>
              <td style="text-align: center;">
                <h1 style="color: #84cc16;">Matchably</h1>
                <h2 style="color: #333;">📦 Your Product Has Shipped!</h2>
                <p style="color: #555; font-size: 16px;">Hi ${userName},</p>
                <p style="color: #555; font-size: 16px;">Great news! Your product for the <strong>${campaignTitle}</strong> campaign has been shipped.</p>
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
                  <p style="margin: 0; color: #333; font-size: 14px;">Tracking Number:</p>
                  <p style="margin: 10px 0; font-size: 18px; font-weight: bold; color: #84cc16;">${trackingNumber}</p>
                </div>
                <a href="${trackingUrl}" target="_blank" style="display: inline-block; margin-top: 20px; padding: 12px 25px; background-color: #84cc16; color: white; text-decoration: none; border-radius: 5px; font-size: 16px;">
                  Track Your Shipment
                </a>
                <p style="margin-top: 30px; color: #777;">You can also track your shipment by visiting:</p>
                <p style="word-break: break-all; color: #84cc16;">
                  <a href="${trackingUrl}" style="color: #84cc16;">${trackingUrl}</a>
                </p>
                <p style="color: #999; margin-top: 30px;">Thank you for being part of our campaign!</p>
              </td>
            </tr>
          </table>
          <p style="text-align: center; color: #aaa; font-size: 12px; margin-top: 20px;">&copy; ${new Date().getFullYear()} Matchably. All rights reserved.</p>
        </body>
        </html>
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Tracking email sent to ${userEmail}`);
  } catch (error) {
    console.error(`❌ Error sending tracking email to ${userEmail}:`, error.message || error);
  }
};

// ✅ Send approval notification email
const sendApprovalEmail = async (userEmail, userName, campaignTitle) => {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: userEmail,
      subject: "You're selected!",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>Application Approved</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
          <table align="center" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
            <tr>
              <td style="text-align: center;">
                <h1 style="color: #84cc16;">Matchably</h1>
                <h2 style="color: #333;">🎉 Congratulations! You're Selected!</h2>
                <p style="color: #555; font-size: 16px;">Hi ${userName},</p>
                <p style="color: #555; font-size: 16px;">Fantastic news! Your application for the <strong>${campaignTitle}</strong> campaign has been approved.</p>
                <div style="background-color: #dcfce7; padding: 20px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #84cc16;">
                  <p style="margin: 0; color: #166534; font-size: 16px; font-weight: bold;">✅ Application Status: APPROVED</p>
                </div>
                <p style="color: #555; font-size: 16px;">The brand will be in touch with you soon regarding the next steps, including product shipment details.</p>
                <p style="color: #555; font-size: 16px;">Please log in to your dashboard to view campaign details and track your progress.</p>
                <a href="https://matchably.kr/signin" target="_blank" style="display: inline-block; margin-top: 20px; padding: 12px 25px; background-color: #84cc16; color: white; text-decoration: none; border-radius: 5px; font-size: 16px;">
                  View Dashboard
                </a>
                <p style="color: #999; margin-top: 30px;">Thank you for being part of our community!</p>
              </td>
            </tr>
          </table>
          <p style="text-align: center; color: #aaa; font-size: 12px; margin-top: 20px;">&copy; ${new Date().getFullYear()} Matchably. All rights reserved.</p>
        </body>
        </html>
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Approval email sent to ${userEmail}`);
  } catch (error) {
    console.error(`❌ Error sending approval email to ${userEmail}:`, error.message || error);
  }
};

// ✅ Send rejection notification email
const sendRejectionEmail = async (userEmail, userName, campaignTitle) => {
  try {
    const mailOptions = {
      from: SMTP_SEND_EMAIL,
      to: userEmail,
      subject: "Thank you for applying",
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>Application Update</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
          <table align="center" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">
            <tr>
              <td style="text-align: center;">
                <h1 style="color: #84cc16;">Matchably</h1>
                <h2 style="color: #333;">Thank You for Your Application</h2>
                <p style="color: #555; font-size: 16px;">Hi ${userName},</p>
                <p style="color: #555; font-size: 16px;">Thank you for your interest in the <strong>${campaignTitle}</strong> campaign.</p>
                <p style="color: #555; font-size: 16px;">After careful consideration, we've decided to move forward with other creators for this particular campaign. This decision was not easy, as we received many high-quality applications.</p>
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin: 20px 0;">
                  <p style="margin: 0; color: #666; font-size: 16px;">We encourage you to keep applying for future campaigns that match your profile and interests.</p>
                </div>
                <p style="color: #555; font-size: 16px;">We appreciate your time and effort in applying, and we look forward to potentially working together on future opportunities.</p>
                <a href="https://matchably.kr/campaigns" target="_blank" style="display: inline-block; margin-top: 20px; padding: 12px 25px; background-color: #84cc16; color: white; text-decoration: none; border-radius: 5px; font-size: 16px;">
                  Browse New Campaigns
                </a>
                <p style="color: #999; margin-top: 30px;">Thank you for being part of our community!</p>
              </td>
            </tr>
          </table>
          <p style="text-align: center; color: #aaa; font-size: 12px; margin-top: 20px;">&copy; ${new Date().getFullYear()} Matchably. All rights reserved.</p>
        </body>
        </html>
      `,
    };

    await transporter.sendMail(mailOptions);
    console.log(`✅ Rejection email sent to ${userEmail}`);
  } catch (error) {
    console.error(`❌ Error sending rejection email to ${userEmail}:`, error.message || error);
  }
};

module.exports = {
  sendWelcomeEmail,
  sendCampaignToAllUsers,
  sendThanksApplyig,
  sendVerificationLink,
  sendTempPasswordEmail,
  sendTrackingEmail,
  sendApprovalEmail,
  sendRejectionEmail
};
