const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");

const brandTaskRouter = express.Router();

// Mock task data - in real implementation, this would come from database queries
const generateMockTasks = () => {
  return [
    {
      id: 'task_001',
      type: 'tracking',
      campaignId: 1,
      campaignTitle: 'Glow Serum Ampoule',
      creatorName: '<PERSON>',
      creatorId: 2,
      description: 'Enter tracking number for <PERSON>',
      priority: 'high',
      createdAt: '2025-06-19T10:30:00Z',
      dueDate: '2025-06-21T23:59:59Z',
      metadata: {
        creatorEmail: '<EMAIL>',
        socialId: '@jessbeauty',
        platform: 'TikTok'
      }
    },
    {
      id: 'task_002',
      type: 'content',
      campaignId: 1,
      campaignTitle: 'Glow Serum Ampoule',
      creatorName: '<PERSON>',
      creatorId: 1,
      description: 'Review submitted content from <PERSON>',
      priority: 'medium',
      createdAt: '2025-06-18T14:20:00Z',
      dueDate: '2025-06-22T23:59:59Z',
      metadata: {
        contentUrls: {
          instagram: 'https://instagram.com/p/abc123',
          tiktok: null
        },
        submittedAt: '2025-06-18T14:20:00Z'
      }
    },
    {
      id: 'task_003',
      type: 'extension',
      campaignId: 1,
      campaignTitle: 'Glow Serum Ampoule',
      creatorName: 'Emma Chen',
      creatorId: 3,
      description: 'Review extension request (+2 days) from Emma Chen',
      priority: 'medium',
      createdAt: '2025-06-17T09:15:00Z',
      dueDate: '2025-06-20T23:59:59Z',
      metadata: {
        extensionDays: 2,
        reason: 'Need more time for product testing and content creation',
        requestedAt: '2025-06-17T09:15:00Z'
      }
    },
    {
      id: 'task_004',
      type: 'application',
      campaignId: 4,
      campaignTitle: 'Vitamin C Essence',
      creatorName: 'Alex Johnson',
      creatorId: 5,
      description: 'Review application from Alex Johnson',
      priority: 'low',
      createdAt: '2025-06-16T16:45:00Z',
      dueDate: '2025-06-23T23:59:59Z',
      metadata: {
        appliedAt: '2025-06-16T16:45:00Z',
        socialId: '@alexskincare',
        platform: 'Instagram',
        followerCount: 15000
      }
    },
    {
      id: 'task_005',
      type: 'tracking',
      campaignId: 4,
      campaignTitle: 'Vitamin C Essence',
      creatorName: 'Mia Park',
      creatorId: 4,
      description: 'Product delivered 3 days ago - check content submission',
      priority: 'high',
      createdAt: '2025-06-15T12:00:00Z',
      dueDate: '2025-06-20T23:59:59Z',
      metadata: {
        deliveredAt: '2025-06-15T12:00:00Z',
        trackingNumber: 'TW456789123KR',
        daysSinceDelivery: 3
      }
    },
    {
      id: 'task_006',
      type: 'recruitment',
      campaignId: 2,
      campaignTitle: 'Vita Bright Toner Pack',
      creatorName: null,
      creatorId: null,
      description: 'Campaign has 0 applications - needs promotion',
      priority: 'medium',
      createdAt: '2025-06-14T08:30:00Z',
      dueDate: '2025-06-25T23:59:59Z',
      metadata: {
        applicationCount: 0,
        campaignStatus: 'draft',
        targetCreators: 10
      }
    }
  ];
};

// GET /api/brand/tasks - Get all tasks for the authenticated brand
brandTaskRouter.get("/", async (req, res) => {
  try {
    const { type, search } = req.query;
    
    let tasks = generateMockTasks();
    
    // Filter by task type if specified
    if (type && type !== 'all') {
      tasks = tasks.filter(task => task.type === type);
    }
    
    // Filter by campaign search if specified
    if (search) {
      const searchLower = search.toLowerCase();
      tasks = tasks.filter(task => 
        task.campaignTitle.toLowerCase().includes(searchLower)
      );
    }
    
    // Sort by priority and creation date
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    tasks.sort((a, b) => {
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }
      return new Date(b.createdAt) - new Date(a.createdAt);
    });

    res.json({
      status: "success",
      tasks: tasks,
      summary: {
        total: tasks.length,
        byType: {
          tracking: tasks.filter(t => t.type === 'tracking').length,
          content: tasks.filter(t => t.type === 'content').length,
          extension: tasks.filter(t => t.type === 'extension').length,
          application: tasks.filter(t => t.type === 'application').length,
          recruitment: tasks.filter(t => t.type === 'recruitment').length
        },
        byPriority: {
          high: tasks.filter(t => t.priority === 'high').length,
          medium: tasks.filter(t => t.priority === 'medium').length,
          low: tasks.filter(t => t.priority === 'low').length
        }
      }
    });
  } catch (error) {
    console.error("Error fetching brand tasks:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch tasks"
    });
  }
});

// POST /api/brand/tasks/:taskId/complete - Mark a task as completed
brandTaskRouter.post("/:taskId/complete", async (req, res) => {
  try {
    const { taskId } = req.params;
    const { action, notes } = req.body;
    
    // In real implementation, you would:
    // 1. Find the task in database
    // 2. Perform the appropriate action based on task type
    // 3. Update related records (campaigns, creators, etc.)
    // 4. Mark task as completed
    // 5. Send notifications if needed
    
    console.log(`Task ${taskId} completed with action: ${action}`);
    
    res.json({
      status: "success",
      message: "Task completed successfully",
      taskId: taskId,
      action: action
    });
  } catch (error) {
    console.error("Error completing task:", error);
    res.json({
      status: "failed",
      message: "Failed to complete task"
    });
  }
});

// GET /api/brand/tasks/summary - Get task summary for dashboard widget
brandTaskRouter.get("/summary", async (req, res) => {
  try {
    const tasks = generateMockTasks();
    
    // Get high priority tasks for dashboard widget
    const highPriorityTasks = tasks
      .filter(task => task.priority === 'high')
      .slice(0, 5) // Limit to 5 for widget
      .map(task => ({
        id: task.id,
        type: task.type,
        campaignTitle: task.campaignTitle,
        description: task.description,
        dueDate: task.dueDate,
        campaignId: task.campaignId
      }));

    res.json({
      status: "success",
      summary: {
        totalTasks: tasks.length,
        highPriority: tasks.filter(t => t.priority === 'high').length,
        overdue: tasks.filter(t => new Date(t.dueDate) < new Date()).length,
        byType: {
          tracking: tasks.filter(t => t.type === 'tracking').length,
          content: tasks.filter(t => t.type === 'content').length,
          extension: tasks.filter(t => t.type === 'extension').length,
          application: tasks.filter(t => t.type === 'application').length,
          recruitment: tasks.filter(t => t.type === 'recruitment').length
        }
      },
      recentTasks: highPriorityTasks
    });
  } catch (error) {
    console.error("Error fetching task summary:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch task summary"
    });
  }
});

module.exports = brandTaskRouter;
