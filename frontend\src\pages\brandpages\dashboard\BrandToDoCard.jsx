import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { ChevronRightIcon } from "lucide-react";

const BrandToDoCard = ({ campaigns = [] }) => {
  const navigate = useNavigate();
  const [tasks, setTasks] = useState([]);
  const [showMore, setShowMore] = useState(false);

  useEffect(() => {
    generateTasks();
  }, [campaigns]);

  const generateTasks = () => {
    const activeTasks = [];

    // Task 1: Review content submissions (mock data)
    const pendingSubmissions = 3; // This would come from API
    if (pendingSubmissions > 0) {
      activeTasks.push({
        id: 'submissions',
        text: `Review ${pendingSubmissions} content submission${pendingSubmissions > 1 ? 's' : ''}`,
        actionText: 'Review',
        route: '/brand/campaigns', // Would go to submissions page
        priority: 1
      });
    }

    // Task 2: Approve creator applications (mock data)
    const pendingApplications = 2; // This would come from API
    if (pendingApplications > 0) {
      activeTasks.push({
        id: 'applications',
        text: `Approve ${pendingApplications} creator application${pendingApplications > 1 ? 's' : ''}`,
        actionText: 'Review',
        route: '/brand/campaigns', // Would go to applications page
        priority: 2
      });
    }

    // Task 3: Enter tracking number (mock data)
    const needsTracking = 1; // This would come from API
    if (needsTracking > 0) {
      activeTasks.push({
        id: 'tracking',
        text: 'Enter tracking number',
        actionText: 'Input',
        route: '/brand/campaigns', // Would go to tracking page
        priority: 3
      });
    }

    // Task 4: Campaign ending soon
    const endingSoonCampaigns = campaigns.filter(campaign => {
      if (!campaign.deadline) return false;
      const deadline = new Date(campaign.deadline);
      const today = new Date();
      const daysLeft = Math.ceil((deadline - today) / (1000 * 60 * 60 * 24));
      return daysLeft <= 3 && daysLeft > 0;
    });

    if (endingSoonCampaigns.length > 0) {
      activeTasks.push({
        id: 'ending-soon',
        text: `${endingSoonCampaigns.length} campaign${endingSoonCampaigns.length > 1 ? 's' : ''} ending soon`,
        actionText: 'View',
        route: '/brand/campaigns',
        priority: 4
      });
    }

    // Task 5: Draft campaigns to publish
    const draftCampaigns = campaigns.filter(c => c.status === 'draft');
    if (draftCampaigns.length > 0) {
      activeTasks.push({
        id: 'drafts',
        text: `Publish ${draftCampaigns.length} draft campaign${draftCampaigns.length > 1 ? 's' : ''}`,
        actionText: 'Publish',
        route: '/brand/campaigns',
        priority: 5
      });
    }

    // Sort by priority and limit to 5
    activeTasks.sort((a, b) => a.priority - b.priority);
    setTasks(activeTasks);
  };

  const displayedTasks = showMore ? tasks : tasks.slice(0, 5);
  const hasMoreTasks = tasks.length > 5;

  // Empty state
  if (tasks.length === 0) {
    return (
      <div className="bg-[#1f1f1f] border border-[#2c2c2c] rounded-xl p-6 shadow-md">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-white">📋 Brand To-Do</h2>
          <button
            onClick={() => navigate('/brand/tasks')}
            className="text-lime-400 hover:text-lime-300 text-sm underline transition"
          >
            See All Tasks
          </button>
        </div>

        <div className="text-center py-6">
          <div className="text-3xl mb-2">✅</div>
          <p className="text-gray-400 text-sm">
            All caught up! No pending tasks right now.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#1f1f1f] border border-[#2c2c2c] rounded-xl p-6 shadow-md">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-white">📋 Brand To-Do</h2>
        <button
          onClick={() => navigate('/brand/tasks')}
          className="text-lime-400 hover:text-lime-300 text-sm underline transition"
        >
          See All Tasks
        </button>
      </div>

      <ul className="space-y-3">
        {displayedTasks.map((task) => (
          <li
            key={task.id}
            className="flex justify-between items-center group"
          >
            <div className="flex items-center gap-2 text-gray-300 flex-1">
              <span className="text-sm">•</span>
              <span className="text-sm">{task.text}</span>
            </div>

            <button
              onClick={() => navigate(task.route)}
              className="text-xs bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-1.5 rounded-md transition font-medium ml-3 flex-shrink-0"
            >
              {task.actionText}
            </button>
          </li>
        ))}
      </ul>

      {hasMoreTasks && !showMore && (
        <button
          onClick={() => setShowMore(true)}
          className="w-full mt-4 text-sm text-gray-400 hover:text-gray-300 transition flex items-center justify-center gap-1"
        >
          Show More
          <ChevronRightIcon className="w-3 h-3" />
        </button>
      )}
    </div>
  );
};

export default BrandToDoCard;
