<!-- @format -->

<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<link
			rel="icon"
			type="image"
			href="/assets/icon-DrIh75rU.png"
		/>
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1.0"
		/>
		<title>Matchably</title>

		<!-- Email Marketing -->
		<script
			src="https://cdn.brevo.com/js/sdk-loader.js"
			async
		></script>
		<script>
			// Version: 2.0
			window.Brevo = window.Brevo || [];
			Brevo.push([
				'init',
				{
					client_key: 'yvzj9fkskspqrmev6rce0tzd',
					// Optional: Add other initialization options, see documentation
				},
			]);
		</script>

		<!-- Google tag (gtag.js) ✅ -->
		<script
			async
			src="https://www.googletagmanager.com/gtag/js?id=G-1QDNNP5J7P"
		></script>
		<script>
			window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}
			gtag('js', new Date());
			gtag('config', 'G-1QDNNP5J7P');
		</script>

		<script type="text/javascript">
			 (function(c,l,a,r,i,t,y){
			  c[a]=c[a]
			  function(){(c[a].q=c[a].q||[]).push(arguments)};
			  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
			  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
			})(window, document, "clarity", "script", "rify7wkfxx");
		</script>

		<!-- Fonts and CSS -->
		<link
			href="/src/styles.css"
			rel="stylesheet"
		/>
		<link
			rel="preconnect"
			href="https://fonts.googleapis.com"
		/>
		<link
			rel="preconnect"
			href="https://fonts.gstatic.com"
			crossorigin
		/>
		<link
			href="https://fonts.googleapis.com/css2?family=Gidole&family=Noto+Sans:ital,wght@0,100..900;1,100..900&family=Roboto:ital,wght@0,100..900;1,100..900&display=swap"
			rel="stylesheet"
		/>
		<script type="module" crossorigin src="/assets/index-C4eVY4SZ.js"></script>
		<link rel="stylesheet" crossorigin href="/assets/index-BL3THXC0.css">
	</head>
	<body>
		<div id="root"></div>

	</body>
</html>