// routes/admin/brands.js
const express = require('express');
const router  = express.Router();
const { VerifyTokenAuth } = require('../../middlewares/AdminAuth');
const {
  getBrands,
  approveBrand,
  rejectBrand
} = require('../../middlewares/brandApprovalController');

/// Protect all brand routes with admin auth
router.use(VerifyTokenAuth);

// List and filter brands: ?status=pending|approved|all
router.get('/', getBrands);

// Approve a brand
router.patch('/:id/approve', approveBrand);

// Reject (delete) a brand
router.patch('/:id/reject', rejectBrand);

module.exports = router;
