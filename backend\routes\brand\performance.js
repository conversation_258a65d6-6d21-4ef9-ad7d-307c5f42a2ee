const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");
const PerformanceService = require("../../services/performanceService");

const brandPerformanceRouter = express.Router();

// GET /api/brand/performance/:creatorId - Get performance score for a creator
brandPerformanceRouter.get("/:creatorId", VerifyToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    
    const performance = await PerformanceService.getPerformanceDisplay(creatorId);
    
    res.json({
      status: "success",
      data: performance
    });
  } catch (error) {
    console.error("Error fetching performance score:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch performance score"
    });
  }
});

// POST /api/brand/performance/join - Update performance when creator joins campaign
brandPerformanceRouter.post("/join", VerifyToken, async (req, res) => {
  try {
    const { creatorId, campaignId } = req.body;
    
    if (!creatorId || !campaignId) {
      return res.json({
        status: "failed",
        message: "Creator ID and Campaign ID are required"
      });
    }
    
    const performance = await PerformanceService.onCreatorJoinCampaign(creatorId, campaignId);
    
    res.json({
      status: "success",
      message: "Performance updated for campaign join",
      data: performance
    });
  } catch (error) {
    console.error("Error updating performance on join:", error);
    res.json({
      status: "failed",
      message: "Failed to update performance"
    });
  }
});

// PUT /api/brand/performance/status - Update performance when status changes
brandPerformanceRouter.put("/status", VerifyToken, async (req, res) => {
  try {
    const { creatorId, campaignId, status } = req.body;
    
    if (!creatorId || !campaignId || !status) {
      return res.json({
        status: "failed",
        message: "Creator ID, Campaign ID, and status are required"
      });
    }
    
    const performance = await PerformanceService.onStatusChange(creatorId, campaignId, status);
    
    res.json({
      status: "success",
      message: "Performance updated for status change",
      data: performance
    });
  } catch (error) {
    console.error("Error updating performance on status change:", error);
    res.json({
      status: "failed",
      message: "Failed to update performance"
    });
  }
});

// PUT /api/brand/performance/tracking - Update performance when tracking is added
brandPerformanceRouter.put("/tracking", VerifyToken, async (req, res) => {
  try {
    const { creatorId, campaignId, trackingDate } = req.body;
    
    if (!creatorId || !campaignId) {
      return res.json({
        status: "failed",
        message: "Creator ID and Campaign ID are required"
      });
    }
    
    const performance = await PerformanceService.onTrackingInput(
      creatorId, 
      campaignId, 
      trackingDate ? new Date(trackingDate) : new Date()
    );
    
    res.json({
      status: "success",
      message: "Performance updated for tracking input",
      data: performance
    });
  } catch (error) {
    console.error("Error updating performance on tracking:", error);
    res.json({
      status: "failed",
      message: "Failed to update performance"
    });
  }
});

// PUT /api/brand/performance/submission - Update performance when content is submitted
brandPerformanceRouter.put("/submission", VerifyToken, async (req, res) => {
  try {
    const { creatorId, campaignId, submissionDate } = req.body;
    
    if (!creatorId || !campaignId) {
      return res.json({
        status: "failed",
        message: "Creator ID and Campaign ID are required"
      });
    }
    
    const performance = await PerformanceService.onContentSubmission(
      creatorId, 
      campaignId, 
      submissionDate ? new Date(submissionDate) : new Date()
    );
    
    res.json({
      status: "success",
      message: "Performance updated for content submission",
      data: performance
    });
  } catch (error) {
    console.error("Error updating performance on submission:", error);
    res.json({
      status: "failed",
      message: "Failed to update performance"
    });
  }
});

// POST /api/brand/performance/update-expired - Manually trigger update of expired deadlines
brandPerformanceRouter.post("/update-expired", VerifyToken, async (req, res) => {
  try {
    const updatedCount = await PerformanceService.updateExpiredDeadlines();
    
    res.json({
      status: "success",
      message: `Updated ${updatedCount} expired campaign deadlines`,
      data: { updatedCount }
    });
  } catch (error) {
    console.error("Error updating expired deadlines:", error);
    res.json({
      status: "failed",
      message: "Failed to update expired deadlines"
    });
  }
});

// GET /api/brand/performance/bulk/:campaignId - Get performance scores for all creators in a campaign
brandPerformanceRouter.get("/bulk/:campaignId", VerifyToken, async (req, res) => {
  try {
    const { campaignId } = req.params;
    
    // This would typically get all creators for a campaign and their performance scores
    // For now, return mock data structure
    const mockCreators = [
      { id: 'creator1', name: 'Sarah Kim' },
      { id: 'creator2', name: 'Jessica Lee' },
      { id: 'creator3', name: 'Emma Chen' }
    ];
    
    const performanceData = {};
    
    for (const creator of mockCreators) {
      performanceData[creator.id] = await PerformanceService.getPerformanceDisplay(creator.id);
    }
    
    res.json({
      status: "success",
      data: performanceData
    });
  } catch (error) {
    console.error("Error fetching bulk performance scores:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch performance scores"
    });
  }
});

module.exports = brandPerformanceRouter;
