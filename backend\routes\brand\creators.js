const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");
const { sendTrackingEmail, sendApprovalEmail, sendRejectionEmail } = require("../../functions/sendEmail");
const PerformanceService = require("../../services/performanceService");

const brandCreatorRouter = express.Router();

// Mock creator applications data (replace with actual database queries)
const mockCreatorApplications = [
  // Campaign 1 - Glow Serum Ampoule (Active)
  {
    id: 1,
    campaignId: 1,
    creatorId: 'creator1',
    name: '<PERSON>',
    email: '<EMAIL>',
    socialId: '@sarahskincare',
    platform: 'Instagram',
    profileUrl: 'https://instagram.com/sarahskincare',
    performanceScore: 4.2,
    tracking: 'TW123456789KR',
    courier: 'UPS',
    deliveryStatus: '📦 Delivered on Jun 18',
    deliveredAt: '2025-06-18',
    contentStatus: '✅ Submitted',
    extensionStatus: null,
    participationStatus: 'Approved',
    appliedAt: '2025-05-20',
    approvedAt: '2025-05-22',
    shippingInfo: {
      name: '<PERSON>',
      phone: '+82-10-1234-5678',
      address: '123 Gangnam-gu, Seoul, South Korea 06234'
    },
    contentUrls: {
      instagram: 'https://instagram.com/p/abc123',
      tiktok: null
    }
  },
  {
    id: 2,
    campaignId: 1,
    creatorId: 'creator2',
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    socialId: '@jessbeauty',
    platform: 'TikTok',
    profileUrl: 'https://tiktok.com/@jessbeauty',
    performanceScore: null, // Less than 2 campaigns
    tracking: '',
    courier: '',
    deliveryStatus: '❌ Not Found',
    deliveredAt: null,
    contentStatus: '— Not Submitted',
    extensionStatus: '⏳ +3d Pending',
    participationStatus: 'Applied',
    appliedAt: '2025-05-21',
    approvedAt: null,
    shippingInfo: null,
    contentUrls: {
      instagram: null,
      tiktok: null
    }
  },
  {
    id: 3,
    campaignId: 1,
    creatorId: 'creator3',
    name: 'Emma Chen',
    email: '<EMAIL>',
    socialId: '@emmaskintips',
    platform: 'Instagram',
    profileUrl: 'https://instagram.com/emmaskintips',
    performanceScore: 3.9,
    tracking: 'TW987654321KR',
    courier: 'DHL',
    deliveryStatus: '🚚 In Transit',
    deliveredAt: null,
    contentStatus: '— Not Submitted',
    extensionStatus: '✅ +2d Approved',
    participationStatus: 'Approved',
    appliedAt: '2025-05-19',
    approvedAt: '2025-05-21',
    shippingInfo: {
      name: 'Emma Chen',
      phone: '+82-10-9876-5432',
      address: '456 Hongdae, Mapo-gu, Seoul, South Korea 04567'
    },
    contentUrls: {
      instagram: null,
      tiktok: null
    }
  },
  // Campaign 4 - Vitamin C Essence (Active)
  {
    id: 4,
    campaignId: 4,
    creatorId: 'creator4',
    name: 'Mia Park',
    email: '<EMAIL>',
    socialId: '@miabeautylife',
    platform: 'TikTok',
    profileUrl: 'https://tiktok.com/@miabeautylife',
    performanceScore: 4.5,
    tracking: 'TW456789123KR',
    courier: 'FedEx',
    deliveryStatus: '📦 Delivered on Jun 15',
    deliveredAt: '2025-06-15',
    contentStatus: '✅ Submitted',
    extensionStatus: null,
    participationStatus: 'Approved',
    appliedAt: '2025-05-18',
    approvedAt: '2025-05-20',
    shippingInfo: {
      name: 'Mia Park',
      phone: '+82-10-5555-1234',
      address: '789 Itaewon-dong, Yongsan-gu, Seoul, South Korea 04348'
    },
    contentUrls: {
      instagram: null,
      tiktok: 'https://tiktok.com/@miabeautylife/video/123456789'
    }
  },
  {
    id: 5,
    campaignId: 4,
    creatorId: 'creator5',
    name: 'Alex Johnson',
    email: '<EMAIL>',
    socialId: '@alexskincare',
    platform: 'Instagram',
    profileUrl: 'https://instagram.com/alexskincare',
    performanceScore: 3.7,
    tracking: '',
    courier: '',
    deliveryStatus: '❌ Not Found',
    deliveredAt: null,
    contentStatus: '— Not Submitted',
    extensionStatus: null,
    participationStatus: 'Applied',
    appliedAt: '2025-05-22',
    approvedAt: null,
    shippingInfo: null,
    contentUrls: {
      instagram: null,
      tiktok: null
    }
  }
];

// GET /api/brand/creators/:campaignId - Get creators for a specific campaign
brandCreatorRouter.get("/:campaignId", async (req, res) => {
  try {
    const { campaignId } = req.params;
    
    // Filter creators by campaign ID
    const creators = mockCreatorApplications.filter(
      creator => creator.campaignId === parseInt(campaignId)
    );

    res.json({
      status: "success",
      data: creators
    });
  } catch (error) {
    console.error("Error fetching creators:", error);
    res.json({
      status: "failed",
      message: "Failed to fetch creators"
    });
  }
});

// PUT /api/brand/creators/:creatorId/tracking - Update tracking information
brandCreatorRouter.put("/:creatorId/tracking", VerifyToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { trackingNumber, courier } = req.body;

    // Validate input
    if (!trackingNumber || trackingNumber.length > 50) {
      return res.json({
        status: "failed",
        message: "Tracking number is required and must be less than 50 characters"
      });
    }

    // Find the creator application
    const creatorIndex = mockCreatorApplications.findIndex(
      creator => creator.id === parseInt(creatorId)
    );

    if (creatorIndex === -1) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    const creator = mockCreatorApplications[creatorIndex];
    
    // Update tracking information
    mockCreatorApplications[creatorIndex] = {
      ...creator,
      tracking: trackingNumber,
      courier: courier || 'Other',
      trackingUpdatedAt: new Date().toISOString()
    };

    // Send tracking email to creator
    try {
      await sendTrackingEmail(
        creator.email,
        creator.name,
        `Campaign #${creator.campaignId}`, // Replace with actual campaign title
        trackingNumber
      );
    } catch (emailError) {
      console.error("Failed to send tracking email:", emailError);
      // Don't fail the request if email fails
    }

    // Update performance score for tracking input
    try {
      await PerformanceService.onTrackingInput(creator.creatorId, creator.campaignId);
    } catch (performanceError) {
      console.error("Failed to update performance score:", performanceError);
      // Don't fail the request if performance update fails
    }

    res.json({
      status: "success",
      message: "Tracking information updated and email sent",
      data: mockCreatorApplications[creatorIndex]
    });
  } catch (error) {
    console.error("Error updating tracking:", error);
    res.json({
      status: "failed",
      message: "Failed to update tracking information"
    });
  }
});

// PUT /api/brand/creators/:creatorId/status - Update creator application status
brandCreatorRouter.put("/:creatorId/status", VerifyToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ['Applied', 'Approved', 'Rejected', 'Not Submitted', 'Submitted'];
    if (!validStatuses.includes(status)) {
      return res.json({
        status: "failed",
        message: "Invalid status. Must be one of: " + validStatuses.join(', ')
      });
    }

    // Find the creator application
    const creatorIndex = mockCreatorApplications.findIndex(
      creator => creator.id === parseInt(creatorId)
    );

    if (creatorIndex === -1) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    const creator = mockCreatorApplications[creatorIndex];
    const previousStatus = creator.contentStatus;
    
    // Update status
    mockCreatorApplications[creatorIndex] = {
      ...creator,
      contentStatus: status,
      statusUpdatedAt: new Date().toISOString(),
      ...(status === 'Approved' && { approvedAt: new Date().toISOString() }),
      ...(status === 'Rejected' && { rejectedAt: new Date().toISOString() })
    };

    // Send appropriate email based on status change
    try {
      if (status === 'Approved' && previousStatus !== 'Approved') {
        await sendApprovalEmail(
          creator.email,
          creator.name,
          `Campaign #${creator.campaignId}` // Replace with actual campaign title
        );
      } else if (status === 'Rejected' && previousStatus !== 'Rejected') {
        await sendRejectionEmail(
          creator.email,
          creator.name,
          `Campaign #${creator.campaignId}` // Replace with actual campaign title
        );
      }
    } catch (emailError) {
      console.error("Failed to send status email:", emailError);
      // Don't fail the request if email fails
    }

    // Update performance score for status change
    try {
      await PerformanceService.onStatusChange(creator.creatorId, creator.campaignId, status);
    } catch (performanceError) {
      console.error("Failed to update performance score:", performanceError);
      // Don't fail the request if performance update fails
    }

    res.json({
      status: "success",
      message: `Creator status updated to ${status}${status === 'Approved' || status === 'Rejected' ? ' and email sent' : ''}`,
      data: mockCreatorApplications[creatorIndex]
    });
  } catch (error) {
    console.error("Error updating creator status:", error);
    res.json({
      status: "failed",
      message: "Failed to update creator status"
    });
  }
});

// PUT /api/brand/creators/:creatorId/participation - Update participation status
brandCreatorRouter.put("/:creatorId/participation", VerifyToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { action } = req.body; // 'approve' or 'reject'

    // Validate action
    if (!['approve', 'reject'].includes(action)) {
      return res.json({
        status: "failed",
        message: "Invalid action. Must be 'approve' or 'reject'"
      });
    }

    // Find the creator application
    const creatorIndex = mockCreatorApplications.findIndex(
      creator => creator.id === parseInt(creatorId)
    );

    if (creatorIndex === -1) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    const creator = mockCreatorApplications[creatorIndex];

    // Update participation status
    mockCreatorApplications[creatorIndex] = {
      ...creator,
      participationStatus: action === 'approve' ? 'Approved' : 'Rejected',
      approvedAt: action === 'approve' ? new Date().toISOString().split('T')[0] : null,
      rejectedAt: action === 'reject' ? new Date().toISOString().split('T')[0] : null
    };

    res.json({
      status: "success",
      message: `Participation ${action}d successfully`,
      data: mockCreatorApplications[creatorIndex]
    });
  } catch (error) {
    console.error("Error updating participation:", error);
    res.json({
      status: "failed",
      message: "Failed to update participation status"
    });
  }
});

// PUT /api/brand/creators/:creatorId/extension - Handle extension requests
brandCreatorRouter.put("/:creatorId/extension", VerifyToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { action } = req.body; // 'approve' or 'reject'

    // Validate action
    if (!['approve', 'reject'].includes(action)) {
      return res.json({
        status: "failed",
        message: "Invalid action. Must be 'approve' or 'reject'"
      });
    }

    // Find the creator application
    const creatorIndex = mockCreatorApplications.findIndex(
      creator => creator.id === parseInt(creatorId)
    );

    if (creatorIndex === -1) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    const creator = mockCreatorApplications[creatorIndex];

    // Update extension status
    const currentExtension = creator.extensionStatus || '';
    const newExtensionStatus = currentExtension.replace('Pending', action === 'approve' ? 'Approved' : 'Rejected');

    mockCreatorApplications[creatorIndex] = {
      ...creator,
      extensionStatus: newExtensionStatus,
      extensionUpdatedAt: new Date().toISOString()
    };

    res.json({
      status: "success",
      message: `Extension ${action}d successfully`,
      data: mockCreatorApplications[creatorIndex]
    });
  } catch (error) {
    console.error("Error handling extension:", error);
    res.json({
      status: "failed",
      message: "Failed to update extension status"
    });
  }
});

// PUT /api/brand/creators/:creatorId/content-status - Update content status
brandCreatorRouter.put("/:creatorId/content-status", VerifyToken, async (req, res) => {
  try {
    const { creatorId } = req.params;
    const { status } = req.body;

    // Validate status
    const validStatuses = ['Pending', 'Approved', 'Rejected'];
    if (!validStatuses.includes(status)) {
      return res.json({
        status: "failed",
        message: "Invalid status. Must be one of: " + validStatuses.join(', ')
      });
    }

    // Find the creator application
    const creatorIndex = mockCreatorApplications.findIndex(
      creator => creator.id === parseInt(creatorId)
    );

    if (creatorIndex === -1) {
      return res.json({
        status: "failed",
        message: "Creator not found"
      });
    }

    const creator = mockCreatorApplications[creatorIndex];

    // Update content status
    mockCreatorApplications[creatorIndex] = {
      ...creator,
      contentStatus: status === 'Approved' ? '✅ Submitted' : status === 'Rejected' ? '❌ Rejected' : 'Pending',
      contentStatusUpdatedAt: new Date().toISOString()
    };

    res.json({
      status: "success",
      message: `Content status updated to ${status}`,
      data: mockCreatorApplications[creatorIndex]
    });
  } catch (error) {
    console.error("Error updating content status:", error);
    res.json({
      status: "failed",
      message: "Failed to update content status"
    });
  }
});

module.exports = brandCreatorRouter;
