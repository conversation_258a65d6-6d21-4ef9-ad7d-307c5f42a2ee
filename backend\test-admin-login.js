const axios = require('axios');

async function testAdminLogin() {
  console.log('🔍 Testing Admin Login...');
  
  try {
    const response = await axios.post('http://localhost:2340/api/admin/auth', {
      username: '<EMAIL>',
      password: 'Wjdalsdnd0145!!'
    });
    
    if (response.data.status === 'success') {
      console.log('✅ ADMIN LOGIN SUCCESSFUL!');
      console.log('👤 Username: <EMAIL>');
      console.log('🔐 Password: Wjdalsdnd0145!!');
      console.log('🔗 Admin Login URL: http://localhost:3000/admin/auth');
      console.log('🎯 Token generated successfully');
      
      // Test admin verification
      const verifyResponse = await axios.get('http://localhost:2340/api/admin/verify', {
        headers: { authorization: response.data.token }
      });
      
      if (verifyResponse.data.status === 'success') {
        console.log('✅ Admin token verification successful');
        console.log('\n🚀 You can now access the admin dashboard!');
      } else {
        console.log('❌ Admin token verification failed:', verifyResponse.data.message);
      }
      
    } else {
      console.log('❌ Admin login failed:', response.data.message);
    }
  } catch (error) {
    console.log('❌ Connection error:', error.message);
    console.log('💡 Make sure the backend server is running on port 2340');
  }
}

testAdminLogin();
