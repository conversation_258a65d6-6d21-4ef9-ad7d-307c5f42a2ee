const axios = require('axios');

async function testBackendConnection() {
  try {
    console.log('🔍 Testing backend connection...');
    
    // Test health endpoint
    const healthResponse = await axios.get('http://localhost:2340/health');
    console.log('✅ Health check:', healthResponse.data);
    
    // Test root endpoint
    const rootResponse = await axios.get('http://localhost:2340/');
    console.log('✅ Root endpoint:', rootResponse.data);
    
    return true;
  } catch (error) {
    console.error('❌ Backend connection failed:', error.message);
    return false;
  }
}

async function testBrandLogin() {
  try {
    console.log('🔍 Testing brand login...');
    
    // Test with demo credentials
    const loginData = {
      email: '<EMAIL>',
      password: 'DemoUser2024!@#'
    };
    
    console.log('📤 Attempting login with:', loginData.email);
    
    const response = await axios.post('http://localhost:2340/api/auth/signin', loginData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('📦 Login response:', response.data);
    
    if (response.data.status === 'success') {
      console.log('✅ Login successful!');
      console.log('🔑 Token:', response.data.token);
      
      // Test accessing brand campaigns with the token
      const campaignsResponse = await axios.get('http://localhost:2340/api/brand/campaigns', {
        headers: {
          'Authorization': `Bearer ${response.data.token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('📊 Campaigns response:', campaignsResponse.data);
      
      // Test empty campaigns
      const emptyCampaignsResponse = await axios.get('http://localhost:2340/api/brand/campaigns?empty=true', {
        headers: {
          'Authorization': `Bearer ${response.data.token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('📊 Empty campaigns response:', emptyCampaignsResponse.data);
      
    } else {
      console.log('❌ Login failed:', response.data.message);
    }
    
  } catch (error) {
    console.error('❌ Login test failed:', error.response?.data || error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting backend and login tests...\n');
  
  const backendConnected = await testBackendConnection();
  
  if (backendConnected) {
    console.log('\n');
    await testBrandLogin();
  }
  
  console.log('\n✅ Tests completed!');
}

runTests();
