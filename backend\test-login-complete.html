<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brand Login Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #1f1f1f;
            color: white;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
        }
        input {
            width: 100%;
            padding: 10px;
            background-color: #333;
            border: 1px solid #555;
            border-radius: 5px;
            color: white;
        }
        button {
            background-color: #4f46e5;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            width: 100%;
            font-size: 16px;
        }
        button:hover {
            background-color: #4338ca;
        }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #065f46;
            border: 1px solid #10b981;
        }
        .error {
            background-color: #7f1d1d;
            border: 1px solid #ef4444;
        }
        .info {
            background-color: #1e3a8a;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <h1>🔐 Brand Login Test</h1>
    <p>This page tests the brand login functionality directly.</p>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="email">Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="SecurePass2024!@#$" required>
        </div>
        
        <button type="submit" id="loginBtn">Test Login</button>
    </form>
    
    <div id="result"></div>
    
    <div class="info" style="margin-top: 30px;">
        <h3>📋 Test Credentials:</h3>
        <p><strong>Email:</strong> <EMAIL></p>
        <p><strong>Password:</strong> SecurePass2024!@#$</p>
        <p><strong>Backend URL:</strong> http://localhost:2340</p>
        <p><strong>Frontend URL:</strong> http://localhost:3001</p>
    </div>

    <script>
        const BACKEND_URL = 'http://localhost:2340';
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            const loginBtn = document.getElementById('loginBtn');
            
            loginBtn.disabled = true;
            loginBtn.textContent = 'Testing...';
            
            resultDiv.innerHTML = '🔍 Testing login...\n';
            resultDiv.className = 'result info';
            
            try {
                console.log('🔍 Attempting login with:', { email, backend: BACKEND_URL });
                
                const response = await fetch(`${BACKEND_URL}/api/auth/signin`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ email, password }),
                });
                
                console.log('📡 Response status:', response.status);
                console.log('📡 Response headers:', [...response.headers.entries()]);
                
                if (!response.ok) {
                    const errorText = await response.text();
                    console.error('❌ HTTP Error:', response.status, errorText);
                    
                    resultDiv.innerHTML = `❌ HTTP Error: ${response.status}\n${errorText}`;
                    resultDiv.className = 'result error';
                    return;
                }
                
                const data = await response.json();
                console.log('📦 Response data:', data);
                
                if (data.status === 'success') {
                    resultDiv.innerHTML = `✅ LOGIN SUCCESSFUL!\n\n` +
                        `📧 Email: ${email}\n` +
                        `🎯 Token: ${data.token ? 'Generated ✅' : 'Missing ❌'}\n` +
                        `👤 User: ${data.user?.name || 'Unknown'}\n` +
                        `📅 Timestamp: ${new Date().toISOString()}\n\n` +
                        `🔗 You can now access: http://localhost:3001/brand-auth\n` +
                        `🚀 Dashboard: http://localhost:3001/brand/dashboard`;
                    resultDiv.className = 'result success';
                    
                    // Store token for testing
                    localStorage.setItem('BRAND_TOKEN', data.token);
                } else {
                    resultDiv.innerHTML = `❌ LOGIN FAILED\n\n` +
                        `Status: ${data.status}\n` +
                        `Message: ${data.message || 'Unknown error'}`;
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                console.error('💥 Network/Parse error:', error);
                
                let errorMessage = '💥 NETWORK ERROR\n\n';
                
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    errorMessage += 'Cannot connect to server.\n' +
                        'Please check:\n' +
                        '1. Backend server is running on port 2340\n' +
                        '2. CORS is properly configured\n' +
                        '3. No firewall blocking the connection';
                } else {
                    errorMessage += `Error: ${error.message}\n` +
                        `Type: ${error.name}`;
                }
                
                resultDiv.innerHTML = errorMessage;
                resultDiv.className = 'result error';
            } finally {
                loginBtn.disabled = false;
                loginBtn.textContent = 'Test Login';
            }
        });
        
        // Test server connectivity on page load
        window.addEventListener('load', async () => {
            try {
                const response = await fetch(`${BACKEND_URL}/health`);
                const data = await response.json();
                console.log('🏥 Health check:', data);
            } catch (error) {
                console.error('🏥 Health check failed:', error);
            }
        });
    </script>
</body>
</html>
