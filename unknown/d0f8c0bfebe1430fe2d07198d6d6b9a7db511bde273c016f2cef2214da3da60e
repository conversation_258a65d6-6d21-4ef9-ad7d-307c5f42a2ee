const { appliedCampaigns, Campaign, campaignSubmission, User } = require("../../database/index");
const { handleFirstContentApproval } = require("../../services/pointService");
const { sendContentSubmissionReminderEmail } = require("../../functions/sendEmail");

async function getBrandApplicants(req, res) {
  try {
    const { campaignId } = req.params;
    const campaign = await Campaign.findById(campaignId);

    console.log("👉 campaignId param:", campaignId);
    console.log("👉 Logged-in brand ID:", req.user._id);
    console.log("👉 Fetched campaign:", campaign);

    if (!campaign) {
      return res.status(404).json({ status: "failed", message: "Campaign not found" });
    }

    if (campaign.brandId?.toString() !== req.user._id.toString()) {
      console.log("❌ Access denied: brand mismatch");
      return res.status(403).json({ status: "failed", message: "Access denied" });
    }

    const applicants = await appliedCampaigns.find({ campaign: campaignId }).sort({ createdAt: -1 });

    const data = applicants.map(app => ({
      id: app._id,
      name: app.name,
      email: app.email,
      phone: app.phone,
      address: `${app.address}, ${app.city}, ${app.state}, ${app.zipCode}`,
      appliedAt: app.createdAt,
      status: app.status,
      rejectionReason: app.rejectionReason,
      showReasonToInfluencer: app.showReasonToInfluencer
    }));

    return res.json({ status: "success", applicants: data });
  } catch (err) {
    console.error("❌ getBrandApplicants error:", err);
    return res.status(500).json({ status: "failed", message: "Error fetching applicants" });
  }
}

async function updateBrandApplicantStatus(req, res) {
  try {
    const { applicantId } = req.params;
    const { status, rejectionReason, showReasonToInfluencer } = req.body;

    const applicant = await appliedCampaigns.findById(applicantId).populate("campaign");

    console.log("👉 Updating applicant:", applicantId);
    console.log("👉 New status:", status);
    console.log("👉 Logged-in brand ID:", req.user._id);
    console.log("👉 Applicant data:", applicant);

    if (!applicant || applicant.campaign.brandId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ status: "failed", message: "Access denied" });
    }

    if (!["Approved", "Rejected", "Pending"].includes(status)) {
      return res.status(400).json({ status: "failed", message: "Invalid status" });
    }

    applicant.status = status;
    if (status === "Rejected") {
      applicant.rejectionReason = rejectionReason || "";
      applicant.showReasonToInfluencer = !!showReasonToInfluencer;
    } else {
      applicant.rejectionReason = "";
      applicant.showReasonToInfluencer = false;
    }

    await applicant.save();

    if (status === "Approved") {
      await handleFirstContentApproval(applicant.user, applicant.campaign._id);
      await sendContentSubmissionReminderEmail(applicant.email, applicant.name, applicant.campaign.campaignTitle);
    }

    return res.json({ status: "success", message: `Status updated to ${status}` });
  } catch (err) {
    console.error("❌ updateBrandApplicantStatus error:", err);
    return res.status(500).json({ status: "failed", message: "Error updating status" });
  }
}

async function deleteBrandApplicant(req, res) {
  try {
    const { applicantId, campaignId } = req.params;
    const campaign = await Campaign.findById(campaignId);

    console.log("👉 Attempting to delete applicant:", applicantId);
    console.log("👉 Campaign ID:", campaignId);
    console.log("👉 Logged-in brand ID:", req.user._id);

    if (!campaign) {
      return res.status(404).json({ status: "failed", message: "Campaign not found" });
    }

    if (campaign.brandId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ status: "failed", message: "Access denied" });
    }

    await appliedCampaigns.findByIdAndDelete(applicantId);

    return res.json({ status: "success", message: "Applicant deleted" });
  } catch (err) {
    console.error("❌ deleteBrandApplicant error:", err);
    return res.status(500).json({ status: "failed", message: "Error deleting applicant" });
  }
}

module.exports = {
  getBrandApplicants,
  updateBrandApplicantStatus,
  deleteBrandApplicant
};
