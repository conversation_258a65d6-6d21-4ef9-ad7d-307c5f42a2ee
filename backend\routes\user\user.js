const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");
const { ApplyCampaign } = require("../../middlewares/ApplyCampaign");
const { appliedCampaigns, User, campaignSubmission} = require("../../database"); // ✅ include User model

const userRouter = express.Router();
const rateLimit = require("express-rate-limit");
const applyLimiter = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 50, // ✅ Allow up to 100 apply requests per IP
  message: {
    status: "failed",
    message: "Too many apply attempts from this IP. Please try again later.",
  },
});

/**
 * POST /user/apply
 * Apply to a campaign. Requires valid JWT token.
 * Body: { email, address, city, state, zip, campaignId, phone }
 */
userRouter.post("/apply", VerifyToken, applyLimiter, ApplyCampaign);


/**
 * GET /user/appliedCampaigns
 * Fetch all campaigns the authenticated user has applied to.
 * Requires valid JWT token.
 * Body: { email }
 */
userRouter.get("/appliedCampaigns", VerifyToken, async (req, res) => {
  const email = req.user.email;

  try {
    // 1. Get applied campaigns linked to this user
    const results = await appliedCampaigns
      .find({ email })
      .populate({
        path: "campaign",
        select: "campaignTitle recruitmentEndDate status", // ✅ extra fields included
        options: { strictPopulate: false },
      })
      .lean();

    // 2. Filter out entries with null campaign references (in case deleted)
    const validApplications = results.filter((entry) => entry.campaign !== null);

    // 3. Map response with meaningful info
    const response = validApplications.map((item) => {
      const now = new Date();
      const { recruitmentEndDate, status } = item.campaign;

      // ✅ Runtime status check (for expired campaigns)
      const isExpired = recruitmentEndDate && now > new Date(recruitmentEndDate);
      const effectiveStatus = isExpired ? "Deactive" : status;

      return {
        id: item.campaign._id,
        title: item.campaign.campaignTitle,
        campaignStatus: effectiveStatus,
        applicationStatus: item.status,
        rejectionReason: item.rejectionReason || null,
        showReasonToInfluencer: item.showReasonToInfluencer || false,
        appliedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(), // timezone adjust if needed
      };
    });

    return res.json({ status: "success", campaigns: response });
  } catch (error) {
    console.error("❌ Error fetching applied campaigns:", error);
    return res.status(500).json({
      status: "failed",
      message: "Something went wrong while fetching your campaigns.",
    });
  }
});



/**
 * PUT /user/update-social
 * Update social media IDs for the logged-in user
 * Body: { instagramId, youtubeId, tiktokId }
 */
userRouter.put("/update-social", VerifyToken, async (req, res) => {
  const userId = req.user?._id;// ✅ comes from VerifyToken middleware
  const { instagramId, tiktokId } = req.body;

  try {
    await User.findByIdAndUpdate(userId, {
      instagramId,
      tiktokId,
    });

    return res.json({ status: "success", message: "Social media IDs updated successfully" });
  } catch (error) {
    console.error("Error updating social media IDs:", error);
    return res.status(500).json({
      status: "failed",
      message: "Could not update social media info",
    });
  }
});

module.exports = {
  user: userRouter,
};
