const express = require("express");
const { Campaign, appliedCampaigns } = require("../database");
const CampaignsPublic = express.Router();

CampaignsPublic.get("/active", async (req, res) => {
  try {
    const now = new Date();

    // fetch all still-active campaigns
    let campaigns = await Campaign.find({ deadline: { $gte: now } });

    // in-JS sort: first by “startsWith('#')”, then by deadline descending
    campaigns = campaigns
      .sort((a, b) => {
        const aHash = a.brandName.startsWith('#') ? 1 : 0;
        const bHash = b.brandName.startsWith('#') ? 1 : 0;
        if (aHash !== bHash) {
          return bHash - aHash;        // hash-names first
        }
        return b.deadline - a.deadline; // newest deadline next
      })
      .slice(0, 6);                    // limit to 6

    // map out your response
    const result = campaigns.map(campaign => ({
      name:       campaign.campaignTitle,
      brand:      campaign.brandName,
      deadline:   campaign.deadline,
      recruitmentEndDate: campaign.recruitmentEndDate,
      category:   campaign.contentFormat,
      image:      campaign.brandLogo,
      recruiting: campaign.recruiting,
      status:     campaign.status,
      id:         campaign._id,
    }));

    res.json({ status: "success", campaigns: result });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
});


CampaignsPublic.get("/", async (req, res) => {
  const { page = 1 } = req.query;           // default to page 1
  const limit = 6;
  const skip  = (page - 1) * limit;

  try {
    // 1) Build an aggregation pipeline
    const campaigns = await Campaign.aggregate([
      // — Compute hashPriority: 1 if brandName starts with "#", else 0
      {
        $addFields: {
          hashPriority: {
            $cond: [
              { $eq: [ { $substrCP: ["$brandName", 0, 1] }, "#" ] },
              1,
              0
            ]
          }
        }
      },
      // — Globally sort by:
      //     1) hashPriority desc (all “#” brands first)
      //     2) deadline desc
      //     3) _id asc
      {
        $sort: {
          hashPriority: -1,
          deadline:     -1,
          _id:          1
        }
      },
      // — Then paginate
      { $skip: skip },
      { $limit: limit }
    ]);

    if (!campaigns.length) {
      return res.json({
        status:  "failed",
        message: "No campaigns found"
      });
    }

    // 2) Count total for pagination UI
    const totalCampaigns = await Campaign.countDocuments({});
    const totalPages     = Math.ceil(totalCampaigns / limit);

    // 3) Strip out our helper field and reshape payload
    const result = campaigns.map(c => ({
      name:       c.campaignTitle,
      brand:      c.brandName,
      deadline:   c.deadline,
      recruitmentEndDate: c.recruitmentEndDate,
      category:   c.contentFormat,
      image:      c.brandLogo,
      recruiting: c.recruiting,
      status:     c.status,
      id:         c._id
    }));

    // 4) Send response
    res.json({
      status:     "success",
      campaigns:  result,
      totalPages
    });

  } catch (error) {
    res.json({
      status:  "failed",
      message: error.message
    });
  }
});


CampaignsPublic.get("/:id/:email", async (req, res) => {
  const { id, email } = req.params;
  try {
    const campaign = await Campaign.findById(id);
    if (!campaign) {
      return res.status(404).json({ message: "Campaign not found" });
    }

    const Exist = await appliedCampaigns.findOne({email : email, campaign : id})

    const result = {
      campaignTitle: campaign.campaignTitle,
      brandName: campaign.brandName,
      deadline: campaign.deadline,
      contentFormat: campaign.contentFormat,
      productDescription: campaign.productDescription,
      brandLogo: campaign.brandLogo,
      recruiting : campaign.recruiting,
      productImages: campaign.productImages,
      influencersReceive: campaign.influencersReceive,
      participationRequirements: campaign.participationRequirements,
      requiredHashtags: campaign.requiredHashtags,
      applied : Exist ? true : false
    };

    res.json({
      status: "success",
      campaign: result,
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
})

module.exports = {
  CampaignsPublic,
};
