const { CreatorPerformance } = require('../database');

/**
 * Calculate Performance Score (Trust Metric) for a creator
 * Formula: (Number of campaigns submitted on time / Total joined campaigns) × 100
 * 
 * Rules:
 * - Only calculated if creator has joined 2+ campaigns
 * - On-time = content submitted within 14 days of tracking input date
 * - Non-submission = 0% performance for that campaign
 * - Auto-updates on status changes
 */

class PerformanceService {
  
  /**
   * Calculate performance score for a creator
   * @param {String} creatorId - MongoDB ObjectId of creator
   * @returns {Object} { score, totalCampaigns, onTimeCampaigns, hasMinimumCampaigns }
   */
  static async calculatePerformanceScore(creatorId) {
    try {
      // Get all campaigns for this creator
      const campaigns = await CreatorPerformance.find({ creatorId });
      
      const totalCampaigns = campaigns.length;
      
      // If less than 2 campaigns, return N/A
      if (totalCampaigns < 2) {
        return {
          score: null,
          totalCampaigns,
          onTimeCampaigns: 0,
          hasMinimumCampaigns: false,
          display: '—'
        };
      }

      let onTimeCampaigns = 0;
      
      for (const campaign of campaigns) {
        // Only count campaigns where creator was approved and tracking was provided
        if (campaign.status === 'Approved' && campaign.trackingInputDate) {
          
          if (campaign.contentSubmissionDate) {
            // Check if submitted within 14 days of tracking input
            const trackingDate = new Date(campaign.trackingInputDate);
            const submissionDate = new Date(campaign.contentSubmissionDate);
            const daysDifference = Math.floor((submissionDate - trackingDate) / (1000 * 60 * 60 * 24));
            
            if (daysDifference <= 14) {
              onTimeCampaigns++;
              // Update isOnTime flag
              campaign.isOnTime = true;
              await campaign.save();
            } else {
              // Late submission
              campaign.isOnTime = false;
              await campaign.save();
            }
          } else {
            // Check if 14 days have passed since tracking input without submission
            const trackingDate = new Date(campaign.trackingInputDate);
            const now = new Date();
            const daysSinceTracking = Math.floor((now - trackingDate) / (1000 * 60 * 60 * 24));
            
            if (daysSinceTracking > 14) {
              // Mark as late/non-submission
              campaign.isOnTime = false;
              await campaign.save();
            }
            // If still within 14 days, don't count yet (isOnTime remains null)
          }
        }
      }

      // Calculate score
      const score = totalCampaigns > 0 ? Math.round((onTimeCampaigns / totalCampaigns) * 100) : 0;
      
      return {
        score,
        totalCampaigns,
        onTimeCampaigns,
        hasMinimumCampaigns: true,
        display: `${score}%`
      };
      
    } catch (error) {
      console.error('Error calculating performance score:', error);
      return {
        score: null,
        totalCampaigns: 0,
        onTimeCampaigns: 0,
        hasMinimumCampaigns: false,
        display: '—'
      };
    }
  }

  /**
   * Get color indicator for performance score
   * @param {Number} score - Performance score (0-100)
   * @returns {String} Color indicator emoji
   */
  static getScoreColorIndicator(score) {
    if (score === null || score === undefined) return '⚪'; // Gray for N/A
    if (score >= 90) return '🟢'; // Green
    if (score >= 70) return '🟠'; // Orange
    return '🔴'; // Red
  }

  /**
   * Update performance when creator joins a campaign
   * @param {String} creatorId - Creator ID
   * @param {String} campaignId - Campaign ID
   */
  static async onCreatorJoinCampaign(creatorId, campaignId) {
    try {
      // Create or update performance record
      await CreatorPerformance.findOneAndUpdate(
        { creatorId, campaignId },
        { 
          creatorId, 
          campaignId,
          status: 'Applied'
        },
        { upsert: true, new: true }
      );
      
      // Recalculate performance score
      return await this.calculatePerformanceScore(creatorId);
    } catch (error) {
      console.error('Error updating performance on campaign join:', error);
      throw error;
    }
  }

  /**
   * Update performance when creator status changes
   * @param {String} creatorId - Creator ID
   * @param {String} campaignId - Campaign ID
   * @param {String} newStatus - New status
   */
  static async onStatusChange(creatorId, campaignId, newStatus) {
    try {
      await CreatorPerformance.findOneAndUpdate(
        { creatorId, campaignId },
        { status: newStatus },
        { upsert: true, new: true }
      );
      
      // Recalculate performance score
      return await this.calculatePerformanceScore(creatorId);
    } catch (error) {
      console.error('Error updating performance on status change:', error);
      throw error;
    }
  }

  /**
   * Update performance when tracking is added
   * @param {String} creatorId - Creator ID
   * @param {String} campaignId - Campaign ID
   * @param {Date} trackingDate - Date tracking was input
   */
  static async onTrackingInput(creatorId, campaignId, trackingDate = new Date()) {
    try {
      await CreatorPerformance.findOneAndUpdate(
        { creatorId, campaignId },
        { trackingInputDate: trackingDate },
        { upsert: true, new: true }
      );
      
      // Recalculate performance score
      return await this.calculatePerformanceScore(creatorId);
    } catch (error) {
      console.error('Error updating performance on tracking input:', error);
      throw error;
    }
  }

  /**
   * Update performance when content is submitted
   * @param {String} creatorId - Creator ID
   * @param {String} campaignId - Campaign ID
   * @param {Date} submissionDate - Date content was submitted
   */
  static async onContentSubmission(creatorId, campaignId, submissionDate = new Date()) {
    try {
      await CreatorPerformance.findOneAndUpdate(
        { creatorId, campaignId },
        { 
          contentSubmissionDate: submissionDate,
          status: 'Submitted'
        },
        { upsert: true, new: true }
      );
      
      // Recalculate performance score
      return await this.calculatePerformanceScore(creatorId);
    } catch (error) {
      console.error('Error updating performance on content submission:', error);
      throw error;
    }
  }

  /**
   * Get performance data for display in UI
   * @param {String} creatorId - Creator ID
   * @returns {Object} Formatted performance data for UI
   */
  static async getPerformanceDisplay(creatorId) {
    try {
      const performance = await this.calculatePerformanceScore(creatorId);
      
      return {
        score: performance.score,
        display: performance.display,
        colorIndicator: this.getScoreColorIndicator(performance.score),
        totalCampaigns: performance.totalCampaigns,
        onTimeCampaigns: performance.onTimeCampaigns,
        hasMinimumCampaigns: performance.hasMinimumCampaigns,
        tooltip: performance.hasMinimumCampaigns 
          ? `${performance.onTimeCampaigns}/${performance.totalCampaigns} campaigns submitted on time`
          : 'Performance Score is calculated after participating in 2 or more campaigns.'
      };
    } catch (error) {
      console.error('Error getting performance display:', error);
      return {
        score: null,
        display: '—',
        colorIndicator: '⚪',
        totalCampaigns: 0,
        onTimeCampaigns: 0,
        hasMinimumCampaigns: false,
        tooltip: 'Performance Score is calculated after participating in 2 or more campaigns.'
      };
    }
  }

  /**
   * Auto-update performance scores for creators with expired deadlines
   * This should be run periodically (e.g., daily cron job)
   */
  static async updateExpiredDeadlines() {
    try {
      const fourteenDaysAgo = new Date();
      fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);
      
      // Find campaigns where tracking was input 14+ days ago but no content submitted
      const expiredCampaigns = await CreatorPerformance.find({
        trackingInputDate: { $lte: fourteenDaysAgo },
        contentSubmissionDate: null,
        isOnTime: null,
        status: 'Approved'
      });
      
      for (const campaign of expiredCampaigns) {
        // Mark as late submission
        campaign.isOnTime = false;
        await campaign.save();
        
        // Recalculate performance for this creator
        await this.calculatePerformanceScore(campaign.creatorId);
      }
      
      console.log(`✅ Updated ${expiredCampaigns.length} expired campaign deadlines`);
      return expiredCampaigns.length;
    } catch (error) {
      console.error('Error updating expired deadlines:', error);
      throw error;
    }
  }
}

module.exports = PerformanceService;
