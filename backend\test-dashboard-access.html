<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Access Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #1f1f1f;
            color: white;
        }
        .step {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #333;
            border-radius: 10px;
            border-left: 4px solid #4f46e5;
        }
        .success {
            border-left-color: #10b981;
            background-color: #065f46;
        }
        .error {
            border-left-color: #ef4444;
            background-color: #7f1d1d;
        }
        button {
            background-color: #4f46e5;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #4338ca;
        }
        .link {
            color: #60a5fa;
            text-decoration: underline;
            cursor: pointer;
        }
        .credentials {
            background-color: #1e3a8a;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🚀 Brand Dashboard Access Test</h1>
    
    <div class="step">
        <h3>Step 1: Login Test</h3>
        <p>First, let's test the login and get a token:</p>
        <div class="credentials">
            <strong>Email:</strong> <EMAIL><br>
            <strong>Password:</strong> SecurePass2024!@#$
        </div>
        <button onclick="testLogin()">Test Login</button>
        <div id="loginResult"></div>
    </div>
    
    <div class="step">
        <h3>Step 2: Dashboard Access</h3>
        <p>After successful login, test dashboard access:</p>
        <button onclick="testDashboard()">Test Dashboard Access</button>
        <div id="dashboardResult"></div>
    </div>
    
    <div class="step">
        <h3>Step 3: Direct Links</h3>
        <p>Click these links to access the brand dashboard:</p>
        <button onclick="openBrandAuth()">Open Brand Login</button>
        <button onclick="openDashboard()">Open Dashboard (after login)</button>
        <button onclick="openCampaigns()">Open Campaigns (after login)</button>
    </div>
    
    <div class="step">
        <h3>Step 4: Manual Instructions</h3>
        <ol>
            <li>Click "Open Brand Login" above</li>
            <li>Enter credentials:
                <div class="credentials">
                    Email: <EMAIL><br>
                    Password: SecurePass2024!@#$
                </div>
            </li>
            <li>Click "Sign In"</li>
            <li>You should be redirected to the dashboard automatically</li>
            <li>If not, manually go to: <span class="link" onclick="openDashboard()">http://localhost:3001/brand/dashboard</span></li>
        </ol>
    </div>

    <script>
        const BACKEND_URL = 'http://localhost:2340';
        const FRONTEND_URL = 'http://localhost:3001';
        
        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.innerHTML = '🔍 Testing login...';
            
            try {
                const response = await fetch(`${BACKEND_URL}/api/auth/signin`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'SecurePass2024!@#$'
                    }),
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    localStorage.setItem('BRAND_TOKEN', data.token);
                    resultDiv.innerHTML = `
                        <div class="success">
                            ✅ Login Successful!<br>
                            Token: ${data.token ? 'Generated' : 'Missing'}<br>
                            Token stored in localStorage
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            ❌ Login Failed: ${data.message}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ Network Error: ${error.message}
                    </div>
                `;
            }
        }
        
        function testDashboard() {
            const resultDiv = document.getElementById('dashboardResult');
            const token = localStorage.getItem('BRAND_TOKEN');
            
            if (!token) {
                resultDiv.innerHTML = `
                    <div class="error">
                        ❌ No token found. Please login first.
                    </div>
                `;
                return;
            }
            
            resultDiv.innerHTML = `
                <div class="success">
                    ✅ Token found: ${token.substring(0, 20)}...<br>
                    Dashboard should be accessible now.<br>
                    <button onclick="openDashboard()">Open Dashboard</button>
                </div>
            `;
        }
        
        function openBrandAuth() {
            window.open(`${FRONTEND_URL}/brand-auth`, '_blank');
        }
        
        function openDashboard() {
            window.open(`${FRONTEND_URL}/brand/dashboard`, '_blank');
        }
        
        function openCampaigns() {
            window.open(`${FRONTEND_URL}/brand/campaigns`, '_blank');
        }
        
        // Check token on page load
        window.addEventListener('load', () => {
            const token = localStorage.getItem('BRAND_TOKEN');
            if (token) {
                document.getElementById('dashboardResult').innerHTML = `
                    <div class="success">
                        ✅ Existing token found. Dashboard should be accessible.
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
