const { User, appliedCampaigns, Campaign } = require("../database");

async function ApplyCampaign(req, res) {
  const {
    address,
    city,
    state,
    zip,
    campaignId,
    phone,
    instagramId,
    tiktokId
  } = req.body;

  const { email, name, _id, isVerified } = req.user; // ✅ comes from token

  try {

    // ✅ Block unverified users
    if (!isVerified) {
      return res.status(403).json({
        status: "failed",
        message: "Please verify your email before applying to campaigns.",
      });
    }
    // Check if already applied
    const alreadyApplied = await appliedCampaigns.findOne({
      email,
      campaign: campaignId,
    });

    if (alreadyApplied) {
      return res.json({
        status: "failed",
        message: "Already Applied",
      });
    }

    // Verify campaign exists
    const campaignData = await Campaign.findById(campaignId);
    if (!campaignData) {
      return res.json({ status: "failed", message: "Campaign not found" });
    }

    // Determine feedback link (if still needed)
    const feedbackLink =
      campaignData.campaignIndustry === "K-food"
        ? "k-food"
        : campaignData.campaignIndustry === "K-beauty"
        ? "k-beauty"
        : "";

    // Create application record
    const application = new appliedCampaigns({
      name,
      email,
      address,
      state,
      city,
      phone,
      zipCode: zip,
      instagramId: instagramId || "",
      tiktokId: tiktokId || "",
      campaign: campaignId,
    });

    await application.save();

    // Prepare user update: push appliedCampaigns and save social IDs if provided
    const userUpdate = { $push: { appliedCampaigns: application._id } };
    const setFields = {};
    if (instagramId) setFields.instagramId = instagramId;
    if (tiktokId) setFields.tiktokId = tiktokId;
    if (Object.keys(setFields).length) {
      userUpdate.$set = setFields;
    }

    // Update user document
    await User.findByIdAndUpdate(_id, userUpdate);

    return res.json({
      status: "success",
      message: "Successfully applied",
    });
  } catch (error) {
    console.error("❌ ApplyCampaign Error:", error);
    return res.status(500).json({
      status: "failed",
      message: error.message || "Something went wrong",
    });
  }
}

module.exports = {
  ApplyCampaign,
};
