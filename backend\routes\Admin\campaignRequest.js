const express = require("express");
const {
  getPendingRequests,
  approveCampaignRequest,
  rejectCampaignRequest,
} = require("../../middlewares/campaignApprovals");
const { VerifyTokenAuth } = require("../../middlewares/AdminAuth");

const router = express.Router();

/**
 * @route   GET /api/admin/campaign-approvals/pending
 * @desc    Get all pending brand campaign requests
 */
router.get("/pending", VerifyTokenAuth, getPendingRequests);

/**
 * @route   PATCH /api/admin/campaign-approvals/:id/approve
 * @desc    Approve a pending campaign and publish it
 */
router.patch("/:id/approve", VerifyTokenAuth, approveCampaignRequest);

/**
 * @route   PATCH /api/admin/campaign-approvals/:id/reject
 * @desc    Reject a pending campaign request
 */
router.patch("/:id/reject", VerifyTokenAuth, rejectCampaignRequest);

module.exports = router;
