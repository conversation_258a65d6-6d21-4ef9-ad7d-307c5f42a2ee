const express = require("express");
// Auth middleware to protect admin routes
const { VerifyTokenAuth } = require("../../middlewares/AdminAuth");
// User model from database
const { User } = require("../../database");
const mongoose = require("mongoose");

const registered = express.Router();

// GET /admin/users?cursor=...
// Fetch a paginated list of registered users (cursor-based)
registered.get("/", VerifyTokenAuth, async (req, res) => {
  try {
    const limit = 500;
    const cursor = req.query.cursor;
    let query = {};
    if (cursor) {
      // Only fetch users with _id greater than the cursor for next page
      query._id = { $gt: cursor };
    }

    // Fetch limit+1 items to check if there's a next page
    const results = await User.find(query)
      .sort({ _id: 1 })
      .limit(limit + 1);

    const hasMore = results.length > limit;
    const limitedResults = hasMore ? results.slice(0, limit) : results;

    // Map Mongoose documents to plain objects for response
    const users = limitedResults.map((item) => ({
      id: item._id,
      name: item.name,
      email: item.email,
      isVerified: item.isVerified,
      // Adjust createdAt to client timezone if needed
      joinedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(),
      blocked: item.blocked  // include block status
    }));

    res.json({
      status: "success",
      registered: users,
      nextCursor: hasMore ? limitedResults[limitedResults.length - 1]._id : null,
      isLastPage: !hasMore,
    });
  } catch (error) {
    console.error("Error fetching registered users:", error);
    res.status(500).json({ status: "failed", message: "Something went wrong", error: error.message });
  }
});

// POST /admin/users/paginate/
// Alternate cursor pagination using POST body with LastId
registered.post("/paginate/", VerifyTokenAuth, async (req, res) => {
  try {
    const limit = 100;
    const { LastId } = req.body;
    let query = {};
    if (LastId) {
      // Convert string ID to ObjectId type for query
      query._id = { $gt: new mongoose.Types.ObjectId(LastId) };
    }

    const results = await User.find(query)
      .sort({ _id: 1 })
      .limit(limit + 1);

    const hasMore = results.length > limit;
    const limitedResults = hasMore ? results.slice(0, limit) : results;

    const users = limitedResults.map((item) => ({
      id: item._id,
      name: item.name,
      email: item.email,
      isVerified: item.isVerified,
      blocked: item.blocked,
      joinedAt: new Date(item.createdAt.getTime() - 7 * 60 * 60 * 1000).toISOString(),
    }));

    res.json({
      status: "success",
      registered: users,
      nextCursor: hasMore ? limitedResults[limitedResults.length - 1]._id : null,
      isLastPage: !hasMore,
    });
  } catch (error) {
    console.error("Error paginating registered users:", error);
    res.status(500).json({ status: "failed", message: "Something went wrong", error: error.message });
  }
});

// DELETE /admin/users/:id
// Hard-delete a user by ID
registered.delete("/:id", VerifyTokenAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const deleted = await User.findByIdAndDelete(id);
    if (!deleted) {
      return res.json({ status: "failed", message: "User not found" });
    }
    res.json({ status: "success", message: "User deleted" });
  } catch (error) {
    console.error("Error deleting user:", error);
    res.status(500).json({ status: "failed", message: "Something went wrong", error: error.message });
  }
});

// PATCH /admin/users/:id/block
// Toggle blocked status of a user
registered.patch("/:id/block", VerifyTokenAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findById(id);
    if (!user) {
      return res.json({ status: "failed", message: "User not found" });
    }
    user.blocked = !user.blocked;
    await user.save();
    res.json({ status: "success", message: `User ${user.blocked ? 'blocked' : 'unblocked'}` });
  } catch (error) {
    console.error("Error toggling block status:", error);
    res.status(500).json({ status: "failed", message: "Something went wrong", error: error.message });
  }
});

module.exports = {
  registered,
};