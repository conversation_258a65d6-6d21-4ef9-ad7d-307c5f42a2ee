// controllers/brand/trackingController.js
const { ShipmentTracking } = require('../../database/index');
const { sendShipmentEmail } = require('../../functions/sendEmail');

/**
 * POST /brand/tracking/add
 * Add shipment tracking info for a creator
 */
async function addTrackingInfo(req, res) {
  try {
    const { creatorId, campaignId, carrier, trackingNumber } = req.body;
    // Validate input
    if (!creatorId || !campaignId || !carrier || !trackingNumber) {
      return res.status(400).json({ status: 'failed', message: 'creatorId, campaignId, carrier, and trackingNumber are required' });
    }

    // Create tracking record
    const record = await ShipmentTracking.create({
      brand: req.user.id,
      creator: creatorId,
      campaign: campaignId,
      carrier,
      trackingNumber,
      sentOn: new Date()
    });

    // Send notification email asynchronously
    sendShipmentEmail(creatorId, campaignId, carrier, trackingNumber)
      .catch(err => console.error('❌ sendShipmentEmail error:', err));

    return res.json({ status: 'success', record });
  } catch (error) {
    console.error('❌ addTrackingInfo error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error adding tracking info' });
  }
}

/**
 * GET /brand/tracking/creator/:campaignId
 * Get tracking info for the logged-in creator
 */
async function getTrackingForCreator(req, res) {
  try {
    const { campaignId } = req.params;
    if (!campaignId) {
      return res.status(400).json({ status: 'failed', message: 'campaignId is required' });
    }

    const record = await ShipmentTracking.findOne({
      campaign: campaignId,
      creator: req.user.id
    });

    return res.json({ status: 'success', info: record || null });
  } catch (error) {
    console.error('❌ getTrackingForCreator error:', error);
    return res.status(500).json({ status: 'failed', message: 'Server error fetching tracking info' });
  }
}

module.exports = { addTrackingInfo, getTrackingForCreator };
