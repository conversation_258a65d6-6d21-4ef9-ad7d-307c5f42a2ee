const express = require("express");
const { VerifyToken } = require("../../middlewares/auth");
const { VerifyTokenAuth } = require("../../middlewares/AdminAuth");
const { appliedCampaigns, User, campaignSubmission } = require("../../database");

const campaignRouter = express.Router();

// --- CREATE a submission ---
// POST /user/campaign-submission
campaignRouter.post(
  "/campaign-submission",
  VerifyToken,
  async (req, res) => {
    const user_id = req.user._id;
    const {
      campaign_id,
      instagram_urls,
      tiktok_urls,
      allow_brand_reuse,
    } = req.body;

    try {
      // Ensure user is approved
      const application = await appliedCampaigns.findOne({
        campaign: campaign_id,
        email: req.user.email,
        status: "Approved",
      });
      if (!application) {
        return res.status(403).json({
          status: "failed",
          message: "You are not approved to submit for this campaign.",
        });
      }

      // Create
      const submission = await campaignSubmission.create({
        campaign_id,
        user_id,
        email: req.user.email,
        instagram_urls,
        tiktok_urls,
        allow_brand_reuse,
      });

      return res.status(201).json({
        status: "success",
        message: "Submission saved successfully.",
        submission,
      });
    } catch (error) {
      console.error("Error submitting campaign content:", error);
      return res.status(500).json({
        status: "failed",
        message: "Internal server error",
      });
    }
  }
);

// --- READ a submission (user) ---
// GET /user/campaign-submission/:campaignId
campaignRouter.get(
  "/campaign-submission/:campaignId",
  VerifyToken,
  async (req, res) => {
    const user_id = req.user._id;
    const campaign_id = req.params.campaignId;

    try {
      const submission = await campaignSubmission.findOne({
        campaign_id,
        user_id,
      });
      if (!submission) {
        return res.status(404).json({
          status: "failed",
          message: "No submission found for this campaign",
        });
      }

      return res.json({
        status: "success",
        data: {
          instagram_urls: submission.instagram_urls,
          tiktok_urls: submission.tiktok_urls,
          allow_brand_reuse: submission.allow_brand_reuse,
          status: submission.status,
          submitted_at: submission.submitted_at,
        },
      });
    } catch (error) {
      console.error("Error fetching submission:", error);
      return res.status(500).json({
        status: "failed",
        message: "Server error while fetching submission",
      });
    }
  }
);

// --- UPDATE a submission ---
// PUT /user/campaign-submission/:campaignId
campaignRouter.put(
  "/campaign-submission/:campaignId",
  VerifyToken,
  async (req, res) => {
    const user_id = req.user._id;
    const campaign_id = req.params.campaignId;
    const {
      instagram_urls,
      tiktok_urls,
      allow_brand_reuse,
    } = req.body;

    try {
      // Find and ensure ownership
      const submission = await campaignSubmission.findOne({
        campaign_id,
        user_id,
      });
      if (!submission) {
        return res.status(404).json({
          status: "failed",
          message: "No submission found to update",
        });
      }

      // Update fields
      submission.instagram_urls = instagram_urls ?? submission.instagram_urls;
      submission.tiktok_urls = tiktok_urls ?? submission.tiktok_urls;
      submission.allow_brand_reuse =
        allow_brand_reuse ?? submission.allow_brand_reuse;

      await submission.save();

      return res.json({
        status: "success",
        message: "Submission updated successfully.",
        submission,
      });
    } catch (error) {
      console.error("Error updating submission:", error);
      return res.status(500).json({
        status: "failed",
        message: "Server error while updating submission",
      });
    }
  }
);

// --- DELETE a submission ---
// DELETE /user/campaign-submission/:campaignId
campaignRouter.delete(
  "/campaign-submission/:campaignId",
  VerifyToken,
  async (req, res) => {
    const user_id = req.user._id;
    const campaign_id = req.params.campaignId;

    try {
      const result = await campaignSubmission.deleteOne({
        campaign_id,
        user_id,
      });
      if (result.deletedCount === 0) {
        return res.status(404).json({
          status: "failed",
          message: "No submission found to delete",
        });
      }

      return res.json({
        status: "success",
        message: "Submission deleted successfully.",
      });
    } catch (error) {
      console.error("Error deleting submission:", error);
      return res.status(500).json({
        status: "failed",
        message: "Server error while deleting submission",
      });
    }
  }
);

// --- ADMIN: READ any submission by campaignId & email ---
campaignRouter.get(
  "/admin/submission/:campaignId/:email",
  VerifyTokenAuth,
  async (req, res) => {
    const { campaignId, email } = req.params;

    try {
      const submission = await campaignSubmission.findOne({
        campaign_id: campaignId,
        email: decodeURIComponent(email).toLowerCase(),
      });
      if (!submission) {
        return res.status(404).json({
          status: "failed",
          message: "No submission found for this campaign and email",
        });
      }

      return res.json({
        status: "success",
        data: {
          instagram_urls: submission.instagram_urls,
          tiktok_urls: submission.tiktok_urls,
          allow_brand_reuse: submission.allow_brand_reuse,
          status: submission.status,
          submitted_at: submission.submitted_at,
        },
      });
    } catch (error) {
      console.error("Error fetching admin submission:", error);
      return res.status(500).json({
        status: "failed",
        message: "Server error while fetching submission",
      });
    }
  }
);

module.exports = campaignRouter;
